"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import {
    BookOpen,
    HelpCircle,
    LogOut,
    Logs,
    Plus,
    Settings,
    User,
    Workflow
} from "lucide-react"
import { useState } from "react"

export function OutermostSidebar() {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <div
      className={cn(
        "bg-gray-900 border-r border-gray-700 flex flex-col transition-all duration-200 ease-in-out fixed inset-y-0 left-0 z-10",
        isHovered ? "w-64 shadow-lg main-content-overlay" : "w-16"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Header */}
      <div className="p-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-purple-600 rounded flex items-center justify-center flex-shrink-0">
            <User className="w-4 h-4 text-white" />
          </div>
          {isHovered && (
            <div className="flex-1 transition-opacity duration-200 ml-1">
              <div className="text-white font-medium text-sm whitespace-nowrap overflow-hidden">
                Barun Debnath's workspace
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Workflows Section */}
      <div className="px-4 pb-4">
        <div className="flex items-center justify-between mb-3">
          {isHovered && (
            <span className="text-gray-400 text-sm font-medium">Workflows</span>
          )}
          {isHovered && (
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-400 hover:text-white">
              <Plus className="w-4 h-4" />
            </Button>
          )}
        </div>
        
        {/* Workflow Items */}
        <div className="space-y-2">
          <div className="flex items-center space-x-3 text-white cursor-pointer hover:bg-gray-800 rounded p-2 transition-colors">
            <div className="w-6 h-6 bg-orange-500 rounded flex items-center justify-center flex-shrink-0">
              <span className="text-xs font-medium">A</span>
            </div>
            {isHovered && (
              <div className="ml-1">
                <div className="text-sm whitespace-nowrap overflow-hidden">amber-aurora</div>
                <div className="text-xs text-gray-400 mt-0.5">Saved about 16 hours ago</div>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-3 text-white cursor-pointer hover:bg-gray-800 rounded p-2 transition-colors">
            <div className="w-6 h-6 bg-blue-500 rounded flex items-center justify-center flex-shrink-0">
              <span className="text-xs font-medium">D</span>
            </div>
            {isHovered && (
              <span className="text-sm whitespace-nowrap overflow-hidden ml-1">default-agent</span>
            )}
          </div>
        </div>
      </div>

      {/* Navigation Items */}
      <div className="flex-1 px-4 space-y-2">
        <div className="flex items-center space-x-3 text-gray-400 hover:text-white cursor-pointer py-2 hover:bg-gray-800 rounded transition-colors">
          <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
            <Logs className="w-5 h-5" />
          </div>
          {isHovered && (
            <span className="text-sm whitespace-nowrap ml-1">Logs</span>
          )}
        </div>

        <div className="flex items-center space-x-3 text-gray-400 hover:text-white cursor-pointer py-2 hover:bg-gray-800 rounded transition-colors">
          <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
            <BookOpen className="w-5 h-5" />
          </div>
          {isHovered && (
            <span className="text-sm whitespace-nowrap ml-1">Knowledge</span>
          )}
        </div>

        <div className="flex items-center space-x-3 text-gray-400 hover:text-white cursor-pointer py-2 hover:bg-gray-800 rounded transition-colors">
          <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
            <Settings className="w-5 h-5" />
          </div>
          {isHovered && (
            <span className="text-sm whitespace-nowrap ml-1">Settings</span>
          )}
        </div>
      </div>

      {/* Bottom Section */}
      <div className="p-4 border-t border-gray-700">
        <div className="flex items-center space-x-3 text-gray-400 hover:text-white cursor-pointer py-2 hover:bg-gray-800 rounded transition-colors">
          <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
            <User className="w-5 h-5" />
          </div>
          {isHovered && (
            <span className="text-sm whitespace-nowrap ml-1">Invite members</span>
          )}
        </div>

        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center space-x-3">
            <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
              <div className="w-4 h-4 border border-gray-500 rounded"></div>
            </div>
            {isHovered && (
              <span className="text-gray-400 text-sm ml-1">Copy</span>
            )}
          </div>
          
          {isHovered && (
            <div className="flex space-x-2">
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-400 hover:text-white">
                <HelpCircle className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-gray-400 hover:text-white">
                <div className="w-4 h-4 border border-gray-400 rounded"></div>
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
